// 测试标签识别逻辑
const { FileCoverage } = require('./istanbul-lib-coverage/lib/file-coverage.js');

// 创建一个测试用的FileCoverage实例
const testData = {
    path: 'test.vue',
    statementMap: {},
    fnMap: {},
    branchMap: {},
    s: {},
    f: {},
    b: {},
    lines: [25, 26, 27] // 模拟新增代码行
};

const fileCoverage = new FileCoverage(testData);

// 测试标签识别
const testLines = [
    ':prop="item.prop"',
    ':label="$lang(item.label)"',
    '>',
    '<div>',
    '</div>',
    'v-if="condition"',
    '@click="handler"'
];

console.log('测试标签识别逻辑:');
testLines.forEach(line => {
    const isTag = fileCoverage._isTemplateOrTagCode(line);
    console.log(`"${line}" -> isTag: ${isTag}`);
});

// 测试新增代码文件判断
console.log('\n测试新增代码文件判断:');
console.log('_isNewCodeFile():', fileCoverage._isNewCodeFile());
console.log('_hasAnyExecutionTrace():', fileCoverage._hasAnyExecutionTrace());

// 测试计算新增代码覆盖率
console.log('\n测试计算新增代码覆盖率:');
const incrementalTotals = fileCoverage.computeIncrementalTotals();
console.log('incrementalTotals:', incrementalTotals);
