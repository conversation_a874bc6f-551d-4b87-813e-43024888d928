/*
 Copyright 2012-2015, Yahoo Inc.
 Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.
 */
'use strict';

const percent = require('./percent');
const dataProperties = require('./data-properties');

function blankSummary() {
    const empty = () => ({
        total: 0,
        covered: 0,
        skipped: 0,
        pct: 'Unknown'
    });

    return {
        lines: empty(),
        statements: empty(),
        functions: empty(),
        branches: empty(),
        branchesTrue: empty(),
        incremental: empty()
    };
}

// asserts that a data object "looks like" a summary coverage object
function assertValidSummary(obj) {
    const valid =
        obj && obj.lines && obj.statements && obj.functions && obj.branches && obj.incremental;
    if (!valid) {
        throw new Error(
            'Invalid summary coverage object, missing keys, found:' +
                Object.keys(obj).join(',')
        );
    }
}

/**
 * CoverageSummary provides a summary of code coverage . It exposes 4 properties,
 * `lines`, `statements`, `branches`, and `functions`. Each of these properties
 * is an object that has 4 keys `total`, `covered`, `skipped` and `pct`.
 * `pct` is a percentage number (0-100).
 */
class CoverageSummary {
    /**
     * @constructor
     * @param {Object|CoverageSummary} [obj=undefined] an optional data object or
     * another coverage summary to initialize this object with.
     */
    constructor(obj) {
        if (!obj) {
            this.data = blankSummary();
        } else if (obj instanceof CoverageSummary) {
            this.data = obj.data;
        } else {
            this.data = obj;
        }
        assertValidSummary(this.data);
        console.log(`[DEBUG] CoverageSummary constructor: incremental =`, this.data.incremental);
    }

    /**
     * merges a second summary coverage object into this one
     * @param {CoverageSummary} obj - another coverage summary object
     */
    merge(obj) {
        console.log(`[DEBUG] ===== CoverageSummary.merge CALLED =====`);
        console.log(`[DEBUG] CoverageSummary.merge called with obj:`, obj ? {
            type: obj.constructor.name,
            incremental: obj.incremental,
            hasData: !!obj.data
        } : 'null obj');

        const keys = [
            'lines',
            'statements',
            'branches',
            'functions',
            'branchesTrue',
            'incremental'
        ];
        keys.forEach(key => {
            // 对于incremental字段，需要特殊处理，因为即使值为0的对象也应该被合并
            if (key === 'incremental') {
                // 确保this.data[key]存在且是对象
                if (!this.data[key] || typeof this.data[key] !== 'object') {
                    this.data[key] = { total: 0, covered: 0, skipped: 0, pct: 0 };
                    console.log(`[DEBUG] merge: initialized this.data[${key}] to:`, this.data[key]);
                }

                // 正确访问obj的incremental数据：优先使用obj.data.incremental，然后是obj.incremental
                const objIncremental = (obj.data && obj.data[key]) || obj[key];
                console.log(`[DEBUG] merge: accessing incremental from obj.data[${key}]=`, obj.data ? obj.data[key] : 'no data', 'obj[${key}]=', obj[key], 'final objIncremental=', objIncremental);

                if (objIncremental && typeof objIncremental === 'object') {
                    const before = { ...this.data[key] };
                    this.data[key].total += objIncremental.total || 0;
                    this.data[key].covered += objIncremental.covered || 0;
                    this.data[key].skipped += objIncremental.skipped || 0;
                    this.data[key].pct = percent(this.data[key].covered, this.data[key].total);
                    console.log(`[DEBUG] merge ${key}: before=`, before, 'adding=', objIncremental, 'after=', this.data[key]);
                } else {
                    console.log(`[DEBUG] merge: objIncremental is not a valid object:`, objIncremental, 'keeping current value:', this.data[key]);
                }
            } else if (obj[key]) {
                this.data[key].total += obj[key].total;
                this.data[key].covered += obj[key].covered;
                this.data[key].skipped += obj[key].skipped;
                this.data[key].pct = percent(this.data[key].covered, this.data[key].total);
            }
        });

        return this;
    }

    /**
     * returns a POJO that is JSON serializable. May be used to get the raw
     * summary object.
     */
    toJSON() {
        return this.data;
    }

    /**
     * return true if summary has no lines of code
     */
    isEmpty() {
        return this.lines.total === 0;
    }
}

dataProperties(CoverageSummary, [
    'lines',
    'statements',
    'functions',
    'branches',
    'branchesTrue',
    'incremental'
]);

module.exports = {
    CoverageSummary
};
