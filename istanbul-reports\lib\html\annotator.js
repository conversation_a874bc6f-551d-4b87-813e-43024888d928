// annotator.js - 修复版本，解决重复标记绿色和红色的问题
/*
 Copyright 2012-2015, Yahoo Inc.
 Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.
 */
'use strict'

const InsertionText = require('./insertion-text')
const lt = '\u0001'
const gt = '\u0002'
const RE_LT = /</g
const RE_GT = />/g
const RE_AMP = /&/g
const RE_lt = /\u0001/g
const RE_gt = /\u0002/g

function title(str) {
  return ' title="' + str + '" '
}

function customEscape(text) {
  text = String(text)
  return text
    .replace(RE_AMP, '&amp;')
    .replace(RE_LT, '&lt;')
    .replace(RE_GT, '&gt;')
    .replace(RE_lt, '<')
    .replace(RE_gt, '>')
}

// 优先级常量
const PRIORITY = {
  NONE: 0,
  ORPHAN: 1,
  STATEMENT: 2,
  BRANCH: 3,
  FUNCTION: 4,
  INCREMENT: 5  // 新增语句有最高优先级
}

// 覆盖状态常量
const COVERAGE_STATUS = {
  NOT_COVERED: 'not_covered',    // 红色：完全未覆盖
  PARTIAL_COVERED: 'partial',    // 黄色：部分覆盖
  FULLY_COVERED: 'fully'         // 绿色：完全覆盖
}

// 分析行的覆盖状态
function analyzeCoverageStatus(lineNum, fileCoverage) {
  const lineStats = fileCoverage.getLineCoverage()

  // 检查行级覆盖率
  const lineHits = lineStats[lineNum] || 0
  if (lineHits === 0) {
    return COVERAGE_STATUS.NOT_COVERED
  }

  // 获取覆盖率数据
  const fnStats = fileCoverage.f
  const fnMeta = fileCoverage.fnMap
  const branchStats = fileCoverage.b
  const branchMeta = fileCoverage.branchMap

  // 核心逻辑：如果未执行代码的外层是if语句或者是函数，就标记成部分覆盖

  // 1. 检查当前行是否是函数定义行，且函数内部有未覆盖的行
  if (fnMeta && fnStats) {
    for (const [fName, meta] of Object.entries(fnMeta)) {
      const decl = meta.decl || meta.loc
      if (decl && decl.start && decl.start.line === lineNum) {
        const fnCount = fnStats[fName] || 0
        if (fnCount > 0) {
          // 函数被调用了，检查函数内部是否有未覆盖的行
          const fnStartLine = decl.start.line
          const fnEndLine = decl.end?.line || meta.loc?.end?.line || fnStartLine

          // 检查函数范围内是否有未覆盖的行
          for (let checkLine = fnStartLine; checkLine <= fnEndLine; checkLine++) {
            const checkHits = lineStats[checkLine]
            if (checkHits !== undefined && checkHits === 0) {
              return COVERAGE_STATUS.PARTIAL_COVERED
            }
          }
        }
        break // 找到当前行对应的函数就退出
      }
    }
  }

  // 1.5. 检查当前行是否在某个被执行的函数内，且该函数内部有未覆盖的行
  // 这个逻辑用于处理箭头函数和回调函数的情况
  if (fnMeta && fnStats) {
    for (const [fName, meta] of Object.entries(fnMeta)) {
      const decl = meta.decl || meta.loc
      if (decl && decl.start) {
        const fnStartLine = decl.start.line
        const fnEndLine = decl.end?.line || meta.loc?.end?.line || fnStartLine
        const fnCount = fnStats[fName] || 0

        // 检查当前行是否在这个函数范围内，且函数被调用了
        if (lineNum >= fnStartLine && lineNum <= fnEndLine && fnCount > 0) {
          // 检查函数范围内是否有未覆盖的行
          for (let checkLine = fnStartLine; checkLine <= fnEndLine; checkLine++) {
            const checkHits = lineStats[checkLine]
            if (checkHits !== undefined && checkHits === 0) {
              // 只有当前行被执行了，但函数内部有未执行的行时，才标记为部分覆盖
              if (lineHits > 0) {
                return COVERAGE_STATUS.PARTIAL_COVERED
              }
            }
          }
        }
      }
    }
  }

  // 2. 检查当前行是否是if语句的开始行，且有未覆盖的分支
  if (branchStats && branchMeta) {
    for (const [branchName, branchMetaItem] of Object.entries(branchMeta)) {
      if (branchMetaItem.locations && branchMetaItem.locations.length > 0) {
        const firstLocation = branchMetaItem.locations[0]
        if (firstLocation.start && firstLocation.start.line === lineNum) {
          // 检查这个分支是否有未覆盖的路径
          const branchArray = branchStats[branchName] || []
          const hasUncoveredBranch = branchArray.some(count => count === 0)
          if (hasUncoveredBranch) {
            return COVERAGE_STATUS.PARTIAL_COVERED
          }
          break // 找到当前行对应的分支就退出
        }
      }
    }
  }

  // 3. 简单的相邻行检查：如果当前行被执行，但紧接着的下一行未被执行，可能是函数调用
  const nextLineHits = lineStats[lineNum + 1]
  if (nextLineHits !== undefined && nextLineHits === 0) {
    return COVERAGE_STATUS.PARTIAL_COVERED
  }

  // 默认返回完全覆盖
  return COVERAGE_STATUS.FULLY_COVERED
}

function annotateLines(fileCoverage, structuredText) {
  const lineStats = fileCoverage.getLineCoverage()
  if (!lineStats) {
    return
  }

  // 使用三色系统标记每一行
  Object.entries(lineStats).forEach(([lineNumber, count]) => {
    if (structuredText[lineNumber]) {
      const lineNum = parseInt(lineNumber)
      const coverageStatus = analyzeCoverageStatus(lineNum, fileCoverage)

      // 设置基础状态
      // 根据覆盖状态设置 covered 字段
      if (coverageStatus === COVERAGE_STATUS.PARTIAL_COVERED) {
        structuredText[lineNumber].covered = 'partial'
      } else {
        structuredText[lineNumber].covered = count > 0 ? 'yes' : 'no'
      }
      structuredText[lineNumber].hits = count
      structuredText[lineNumber].coverageStatus = coverageStatus

      // 根据覆盖状态添加相应的背景色 - 这是最高优先级的背景色设置
      let className, titleText

      switch (coverageStatus) {
        case COVERAGE_STATUS.FULLY_COVERED:
          className = 'cstat-yes'
          titleText = `完全覆盖 - 执行${count}次`
          break
        case COVERAGE_STATUS.PARTIAL_COVERED:
          // 使用现有的黄色样式类，而不是可能不存在的 cstat-partial
          className = 'cstat-yes'  // 先用绿色，然后我们添加内联样式
          titleText = `部分覆盖 - 方法被调用但内部有未执行的分支`
          break
        case COVERAGE_STATUS.NOT_COVERED:
          className = 'cstat-no'
          titleText = '未覆盖 - 完全没有执行'
          break
      }

      let openSpan
      if (coverageStatus === COVERAGE_STATUS.PARTIAL_COVERED) {
        // 为部分覆盖状态添加黄色背景的内联样式
        openSpan = lt + 'span class="' + className + '" style="background-color: #fff3c4 !important;"' + title(titleText) + gt
      } else {
        openSpan = lt + 'span class="' + className + '"' + title(titleText) + gt
      }
      const closeSpan = lt + '/span' + gt
      structuredText[lineNumber].text.wrapLine(openSpan, closeSpan)
      structuredText[lineNumber].backgroundApplied = true
      structuredText[lineNumber].annotationPriority = PRIORITY.INCREMENT + 1 // 设置为最高优先级
    }
  })
}

function annotateStatements(fileCoverage, structuredText) {
  const statementStats = fileCoverage.s
  const statementMeta = fileCoverage.statementMap

  Object.entries(statementStats).forEach(([stName, count]) => {
    const meta = statementMeta[stName]
    if (meta.skip) {
      return
    }
    const isCovered = count > 0

    // 只处理未覆盖的语句，添加内联标记
    if (!isCovered) {
      const className = 'cstat-no'
      const titleText = 'statement not covered'
      const openSpan = lt + 'span class="' + className + '"' + title(titleText) + gt
      const closeSpan = lt + '/span' + gt

      for (let lineNum = meta.start.line; lineNum <= meta.end.line; lineNum++) {
        if (structuredText[lineNum]) {
          // 检查优先级，避免覆盖更高优先级的注解
          if (structuredText[lineNum].annotationPriority >= PRIORITY.STATEMENT) {
            continue
          }

          // 只有未覆盖的行才需要添加内联标记
          if (structuredText[lineNum].covered === 'no') {
            const lineTextObj = structuredText[lineNum].text
            let wrapStartCol = 0
            let wrapEndCol = lineTextObj.originalLength()

            if (meta.start.line === meta.end.line) {
              wrapStartCol = meta.start.column
              wrapEndCol = meta.end.column + 1
            } else if (lineNum === meta.start.line) {
              wrapStartCol = meta.start.column
            } else if (lineNum === meta.end.line) {
              wrapEndCol = lineTextObj.originalLength()
            }

            if (wrapStartCol <= wrapEndCol) {
              // 只添加内联标记，不处理行背景（行背景已经在 annotateLines 中处理了）
              lineTextObj.wrap(wrapStartCol, openSpan, wrapEndCol, closeSpan)
            }
            structuredText[lineNum].annotationPriority = PRIORITY.STATEMENT
          }
        }
      }
    }
  })
}

function annotateFunctions(fileCoverage, structuredText) {
  const fnStats = fileCoverage.f
  const fnMeta = fileCoverage.fnMap
  if (!fnStats) return

  Object.entries(fnStats).forEach(([fName, count]) => {
    const meta = fnMeta[fName]
    if (meta.skip) {
      return
    }
    const isCovered = count > 0
    const decl = meta.decl || meta.loc
    const startLine = decl.start.line

    if (!structuredText[startLine]) return

    // 检查优先级
    if (structuredText[startLine].annotationPriority >= PRIORITY.FUNCTION) {
      return
    }

    const className = isCovered ? 'fstat-yes' : 'fstat-no'
    const titleText = isCovered
      ? `function covered ${count} time${count > 1 ? 's' : ''}`
      : 'function not covered'
    const openSpan =
      lt + 'span class="' + className + '"' + title(titleText) + gt
    const closeSpan = lt + '/span' + gt
    const text = structuredText[startLine].text

    const startCol = decl.start.column
    const endCol =
      decl.end.line === startLine ? decl.end.column + 1 : text.originalLength()

    // 添加函数名的内联标记
    text.wrap(startCol, openSpan, startCol < endCol ? endCol : text.originalLength(), closeSpan)

    // 函数覆盖状态已经在 annotateLines 中通过 analyzeCoverageStatus 处理了
    // 这里不需要重复处理背景色，避免覆盖已有的部分覆盖状态

    structuredText[startLine].annotationPriority = PRIORITY.FUNCTION
  })
}

function annotateBranches(fileCoverage, structuredText) {
  const branchStats = fileCoverage.b
  const branchMeta = fileCoverage.branchMap
  if (!branchStats) return

  Object.entries(branchStats).forEach(([branchName, branchArray]) => {
    const sumCount = branchArray.reduce((p, n) => p + n, 0)
    if (sumCount === 0 && branchArray.length > 1) {
      return
    }

    const metaArray = branchMeta[branchName].locations
    if (
      branchMeta[branchName].type === 'if' &&
      branchArray.length === 2 &&
      metaArray.length === 1 &&
      branchArray[1] === 0
    ) {
      metaArray[1] = { start: {}, end: {} }
    }

    for (let i = 0; i < branchArray.length; i += 1) {
      if (branchArray[i] > 0) continue

      let meta = metaArray[i]
      if (!meta) continue

      if (
        meta.start.line === undefined &&
        branchMeta[branchName].type === 'if'
      ) {
        meta = metaArray[i - 1]
      }

      const startLine = meta.start.line
      if (!structuredText[startLine]) continue

      // 检查优先级
      if (structuredText[startLine].annotationPriority >= PRIORITY.BRANCH) {
        continue
      }

      // 只处理未覆盖的分支，添加内联标记
      if (structuredText[startLine].covered === 'no') {
        const text = structuredText[startLine].text
        if (branchMeta[branchName].type === 'if') {
          const titleText = (i === 0 ? 'if' : 'else') + ' path not taken'
          const className = meta.skip
            ? 'skip-if-branch'
            : 'missing-if-branch'
          const span =
            lt +
            'span class="' +
            className +
            '"' +
            title(titleText) +
            gt +
            (i === 0 ? 'I' : 'E') +
            lt +
            '/span' +
            gt
          text.insertAt(meta.start.column, span, true)

          // 不再添加行背景，因为已经在 annotateLines 中处理了
          structuredText[startLine].annotationPriority = PRIORITY.BRANCH
        } else {
          const openSpan =
            lt +
            'span class="branch-' +
            i +
            ' ' +
            (meta.skip ? 'cbranch-skip' : 'cbranch-no') +
            '"' +
            title('branch not covered') +
            gt
          const closeSpan = lt + '/span' + gt
          const endCol =
            meta.end.line === startLine
              ? meta.end.column + 1
              : text.originalLength()
          text.wrap(meta.start.column, openSpan, endCol, closeSpan)
          structuredText[startLine].annotationPriority = PRIORITY.BRANCH
        }
      }
    }
  })
}

function annotateOrphanLines(structuredText) {
  // 孤立行处理已经在 annotateLines 中通过三色系统处理了
  // 这里不需要额外处理，避免重复标记
  structuredText.forEach(item => {
    // 只更新优先级，不添加额外的背景色
    if (item.covered === 'no' &&
      item.backgroundApplied &&
      item.annotationPriority < PRIORITY.ORPHAN) {
      item.annotationPriority = PRIORITY.ORPHAN
    }
  })
}


// 判断是否是Vue组件级别的代码（组件加载时自动执行）
function isVueComponentLevelCode(lineContent, filePath) {
  const trimmed = lineContent.trim()

  // 只处理Vue文件
  if (!filePath || !filePath.endsWith('.vue')) {
    return false
  }

  // 1. 所有import语句（模块加载时执行）
  if (trimmed.startsWith('import ')) {
    return true
  }

  // 2. 组件定义和配置（组件实例化时执行）
  if (trimmed.includes('export default') ||
    trimmed.includes('defineComponent') ||
    trimmed.match(/^\s*name\s*:/) ||
    trimmed.match(/^\s*components\s*:/) ||
    trimmed.match(/^\s*props\s*:/) ||
    trimmed.match(/^\s*emits\s*:/)) {
    return true
  }

  // 3. 组件注册（在components对象中的组件名）
  if (trimmed.match(/^[A-Z][a-zA-Z0-9]*\s*,?\s*$/) ||
    trimmed.match(/^[A-Z][a-zA-Z0-9]*\s*:\s*[A-Z][a-zA-Z0-9]*\s*,?\s*$/)) {
    return true
  }

  // 4. Vue 3 Composition API（setup时执行）
  if (trimmed.includes('defineProps') ||
    trimmed.includes('defineEmits') ||
    trimmed.includes('defineExpose') ||
    trimmed.includes('defineSlots')) {
    return true
  }

  // 5. 生命周期钩子定义（组件生命周期时自动调用）
  if (trimmed.match(/^\s*(created|mounted|beforeMount|beforeUpdate|updated|beforeUnmount|unmounted)\s*\(/)) {
    return true
  }

  // 6. Vue文件的结构标签
  if (trimmed.startsWith('<template') ||
    trimmed.startsWith('<style') ||
    trimmed.startsWith('<script')) {
    return true
  }

  return false
}

// 判断是否是模板或标签代码
function isTemplateOrTagCode(lineContent) {
  const trimmed = lineContent.trim()

  // HTML/XML 标签
  if (trimmed.startsWith('<') && (trimmed.endsWith('>') || trimmed.endsWith('/>'))) {
    return true
  }

  // Vue 模板语法
  if (trimmed.includes('v-if=') || trimmed.includes('v-for=') ||
    trimmed.includes('v-show=') || trimmed.includes('v-model=') ||
    trimmed.includes(':key=') || trimmed.includes('@click=') ||
    trimmed.includes('{{') || trimmed.includes('}}')) {
    return true
  }

  // React JSX 语法
  if (trimmed.includes('className=') || trimmed.includes('onClick=') ||
    trimmed.includes('onChange=') || trimmed.includes('onSubmit=')) {
    return true
  }

  // 模板字符串中的HTML
  if (trimmed.includes('`') && (trimmed.includes('<') || trimmed.includes('>'))) {
    return true
  }

  // CSS 样式 - 更精确的匹配
  if ((trimmed.includes('style="') || trimmed.includes("style='")) ||
    (trimmed.includes('class="') || trimmed.includes("class='"))) {
    return true
  }

  return false
}

// 判断文件是否被执行过的辅助函数
function isFileExecutedHelper(fileCoverage) {
  // 检查是否有任何语句被执行
  const statementStats = fileCoverage.s
  if (statementStats) {
    const hasExecutedStatement = Object.values(statementStats).some(count => count > 0)
    if (hasExecutedStatement) {
      return true
    }
  }

  // 检查是否有任何函数被执行
  const functionStats = fileCoverage.f
  if (functionStats) {
    const hasExecutedFunction = Object.values(functionStats).some(count => count > 0)
    if (hasExecutedFunction) {
      return true
    }
  }

  // 检查是否有任何分支被执行
  const branchStats = fileCoverage.b
  if (branchStats) {
    const hasExecutedBranch = Object.values(branchStats).some(branchArray =>
      branchArray.some(count => count > 0)
    )
    if (hasExecutedBranch) {
      return true
    }
  }

  return false
}

function annotateIncrementStatements(fileCoverage, structuredText) {
  const lines = fileCoverage.data?.lines || []

  // 判断文件是否被执行过（检查是否有任何覆盖率数据）
  const isFileExecuted = isFileExecutedHelper(fileCoverage)

  console.log(`[DEBUG] annotateIncrementStatements for file: ${fileCoverage.path}`)
  console.log(`[DEBUG] isFileExecuted:`, isFileExecuted)

  // 获取原始源代码
  const fs = require('fs')
  let sourceLines = []
  try {
    const sourceText = fs.readFileSync(fileCoverage.path, 'utf8')
    sourceLines = sourceText.split(/\r?\n/)
  } catch (e) {
    // Cannot read source file
  }

  structuredText.forEach(text => {
    if (lines.includes(text.line)) {
      // 使用原始源代码内容，而不是可能已经被HTML包装的内容
      const lineContent = sourceLines[text.line - 1] || text.text.toString().trim()
      const isCommentOrEmpty = lineContent === '' ||
        lineContent.startsWith('//') ||
        lineContent.startsWith('/*') ||
        lineContent.startsWith('*') ||
        lineContent.endsWith('*/')

      text.addStatement = 'yes'

      // 新增语句处理：区分标签代码、Vue组件代码和逻辑代码
      if (!isCommentOrEmpty) {
        // 检查是否是标签代码（HTML/JSX等）
        const isTagCode = isTemplateOrTagCode(lineContent)
        // 检查是否是Vue组件级别代码
        const isVueComponentCode = isVueComponentLevelCode(lineContent, fileCoverage.path)

        console.log(`[DEBUG] annotator - Line ${text.line}: "${lineContent.trim()}", covered: ${text.covered}, isTagCode: ${isTagCode}, isVueComponentCode: ${isVueComponentCode}, isFileExecuted: ${isFileExecuted}`)

        if (text.covered === 'yes' || text.covered === 'partial') {
          // 有覆盖率数据的代码
          const coverageStatus = text.coverageStatus || COVERAGE_STATUS.FULLY_COVERED

          let className, titleText
          switch (coverageStatus) {
            case COVERAGE_STATUS.FULLY_COVERED:
              className = 'cstat-yes'
              titleText = '新增语句 - 已覆盖'
              text.addStatementCovered = 'yes'
              break
            case COVERAGE_STATUS.PARTIAL_COVERED:
              className = 'cstat-yes'  // 使用基础类
              titleText = '新增语句 - 部分覆盖'
              text.addStatementCovered = 'partial'
              break
          }

          let openSpan
          if (coverageStatus === COVERAGE_STATUS.PARTIAL_COVERED) {
            // 为部分覆盖状态添加黄色背景的内联样式
            openSpan = '\u0001span class="' + className + '" style="background-color: #fff3c4 !important;" title="' + titleText + '"\u0002'
          } else {
            openSpan = '\u0001span class="' + className + '" title="' + titleText + '"\u0002'
          }
          const closeSpan = '\u0001/span\u0002'
          text.text.wrapLine(openSpan, closeSpan)
          text.backgroundApplied = true
          text.annotationPriority = PRIORITY.INCREMENT
        } else if (isFileExecuted && (isTagCode || isVueComponentCode)) {
          // 如果文件被执行了且是标签代码或Vue组件代码，则视为已覆盖
          const className = 'cstat-yes'
          const titleText = isTagCode
            ? '新增标签代码 - 页面加载时执行'
            : '新增Vue组件代码 - 组件加载时自动执行'
          text.addStatementCovered = 'yes'

          console.log(`[DEBUG] annotator - Line ${text.line}: "${lineContent.trim()}" marked as covered (tag code)`)

          const openSpan = '\u0001span class="' + className + '" title="' + titleText + '"\u0002'
          const closeSpan = '\u0001/span\u0002'
          text.text.wrapLine(openSpan, closeSpan)
          text.backgroundApplied = true
          text.annotationPriority = PRIORITY.INCREMENT
        }
        // 对于其他未覆盖或无数据的新增语句，以及文件整体未覆盖时的标签/组件代码，不添加任何颜色标记
      }
    }
  })
  return structuredText
}

function annotateSourceCode(fileCoverage, sourceStore) {
  try {
    const sourceText = sourceStore.getSource(fileCoverage.path)
    const code = sourceText.split(/(?:\r?\n)|\r/)
    let count = 0
    const structured = code.map(str => {
      count += 1
      return {
        line: count,
        covered: 'neutral',
        hits: 0,
        text: new InsertionText(str),
        backgroundApplied: false,
        annotationPriority: PRIORITY.NONE
      }
    })
    structured.unshift({
      line: 0,
      covered: null,
      text: new InsertionText(''),
      backgroundApplied: false,
      annotationPriority: PRIORITY.NONE
    })

    // 按优先级顺序执行注解
    // 1. 首先处理行级覆盖率（三色系统）- 最高优先级
    annotateLines(fileCoverage, structured)

    // 2. 处理新增语句（如果有的话）
    annotateIncrementStatements(fileCoverage, structured)

    // 3. 处理内联标记（函数名、语句等），但不覆盖行背景
    annotateFunctions(fileCoverage, structured)
    annotateStatements(fileCoverage, structured)
    annotateBranches(fileCoverage, structured)

    // 4. 最后处理孤立行
    annotateOrphanLines(structured)

    structured.shift()

    const annotatedCode = structured.map(
      item => customEscape(item.text.toString()) || '&nbsp;'
    )
    const lineCoverage = structured.map(item => {
      // 根据覆盖状态设置 covered 字段
      let coveredStatus = item.covered
      if (item.coverageStatus === COVERAGE_STATUS.PARTIAL_COVERED) {
        coveredStatus = 'partial'  // 部分覆盖状态
      }

      return {
        covered: coveredStatus,
        hits: item.hits > 0 ? item.hits + 'x' : '&nbsp;',
        addStatement: item.addStatement || 'no',
        addStatementCovered: item.addStatementCovered || 'no',
        coverageStatus: item.coverageStatus || COVERAGE_STATUS.NOT_COVERED
      }
    })
    return {
      annotatedCode,
      lineCoverage,
      maxLines: structured.length
    }
  } catch (ex) {
    const codeArray = [ex.message]
    const lineCoverageArray = [{ covered: 'no', hits: 0 }]
    String(ex.stack || '')
      .split(/\r?\n/)
      .forEach(line => {
        codeArray.push(line)
        lineCoverageArray.push({ covered: 'no', hits: 0 })
      })
    return {
      annotatedCode: codeArray,
      lineCoverage: lineCoverageArray,
      maxLines: codeArray.length
    }
  }
}

module.exports = annotateSourceCode
